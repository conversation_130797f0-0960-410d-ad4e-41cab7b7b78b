.dropdown {
  position: absolute;
  border: 1px solid rgb(197, 197, 197);
  background: #fff;
  padding: 0;
  color: #0062cc;
  text-align: left;
  border-radius: 0.25rem;
  min-width: 140px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}
.dropdown > p {
  margin: 0;
  padding: 0.375rem 1.5rem;
  border-bottom: 1px solid rgb(235, 235, 235);
  min-width: 140px;
  cursor: pointer;
  transition:
    background-color 0.15s ease-in-out,
    color 0.15s ease-in-out;
  user-select: none;
  display: block;
  width: 100%;
  text-align: left;
}
.dropdown-item:hover,
.dropdown-item:focus {
  color: #fff;
  background-color: #0062cc;
}
.dropdown > p:last-child {
  border-bottom: 0 none;
}

.dropdown-item * {
  pointer-events: none;
}
