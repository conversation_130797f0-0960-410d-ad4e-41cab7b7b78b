import React from 'react';
import { ReactComponent as InitialSignupPageBG } from '../../assets/InitialSignupPageBG.svg';
import { ReactComponent as BridgingTechLogo } from '../../assets/BridgingTechLogoTwo.svg';
import { ReactComponent as Breadcrumb3 } from '../../assets/Breadcrumb3.svg';
import { ReactComponent as AlmostDoneBanner } from '../../assets/AlmostDoneBanner.svg';

import './SignupConfirmation1.css';

const SignupConfirmation1: React.FC = () => {
  return (
    <div>
      <BridgingTechLogo className='logo' />
      <div className='logo-text'>
        <span className='brand-text-orange'>Bridging</span>
        <span className='brand-text-blue'>Tech</span>
        <span className='brand-text-green'>Ed</span>
      </div>
      <div className='login-svg-container'>
        <InitialSignupPageBG
          preserveAspectRatio='none'
          viewBox='0 0 1920 1080'
        />
      </div>
      <Breadcrumb3 className='Bread-crumb3' />
      <AlmostDoneBanner className='Almost-Done-Banner' />
      <div className='Confirmation-Text'>
        <p>
          A confirmation email will be sent shortly. Please have you or your
          parent/guardian check the email inbox. Click on the email and it will
          redirect you to a Bridging Tech Webpage, letting you know your sign up
          is complete!
        </p>
      </div>
    </div>
  );
};

export default SignupConfirmation1;
