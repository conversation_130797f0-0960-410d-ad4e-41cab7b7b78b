import { render, fireEvent, screen } from '@testing-library/react';
import axios from 'axios';
import CourseModule from '../components/CourseModule/CourseModule';

jest.mock('axios', () => {
  return {
    put: jest.fn(),
  };
});

describe('CourseModule Component', () => {
  const defaultProps = {
    title: 'Internet Safety for Kids K-3',
    description: 'Watch a video to learn about internet safety for kids K-3.',
    time: '20 minutes',
    points: '+100 POINTS',
    image: '../assets/test-image.png',
    imageLink:
      'https://www.youtube.com/watch?v=89eCHtFs0XM&ab_channel=IndianaUniversityofPennsylvania',
    titleLink:
      'https://www.youtube.com/watch?v=89eCHtFs0XM&ab_channel=IndianaUniversityofPennsylvania',
  };

  const mockedAxios = axios as jest.Mocked<typeof axios>;

  beforeEach(() => {
    mockedAxios.put.mockClear();

    // Reset mocks and set default mock return value
    mockedAxios.put.mockResolvedValue({
      data: { newTotalPoints: 500, pointsEarned: 100 },
    });
  });

  it('calls givePointsForVideo when titleLink is clicked with userId', () => {
    const userId = '6';
    render(<CourseModule {...defaultProps} userId={userId} />);

    fireEvent.click(screen.getByText(defaultProps.title));

    expect(mockedAxios.put).toHaveBeenCalled();
    expect(mockedAxios.put).toHaveBeenCalledWith(`/api/${userId}/watch_video`, {
      videoLink: defaultProps.titleLink,
    });
  });

  it('does not call givePointsForVideo when clicked without userId', () => {
    render(<CourseModule {...defaultProps} />);

    fireEvent.click(screen.getByText(defaultProps.title));

    expect(mockedAxios.put).not.toHaveBeenCalled();
  });
});
