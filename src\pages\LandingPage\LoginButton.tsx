import React from 'react';
import Button from '../../components/Button/Button';

const LoginButton: React.FC = () => {
  // const [isHovered, setIsHovered] = React.useState(false);
  return (
    <Button
      // onMouseEnter={() => setIsHovered(true)}
      // onMouseLeave={() => setIsHovered(false)}
      buttonVariant='solid'
      buttonStyle={{
        color: 'green', rounded:'lg',size:'md'
      }}
    >
      Log In
    </Button>
  );
};

export default LoginButton;
