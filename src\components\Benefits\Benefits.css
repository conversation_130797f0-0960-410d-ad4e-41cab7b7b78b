.background-container1 {
  background-color: rgb(255, 255, 255);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 87px;
  padding-bottom: 87px;
  padding-left: 53px;
  padding-right: 53px;
  width: 100%;
  height: 100%;
}

.statement-container {
  margin-bottom: 60px;
}

.statement {
  color: #16325c;
  font-size: 48px;
  font-weight: bold;
}

.icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 60px;
  margin-right: 60px;
}

.icon-title {
  color: #16325c;
  font-family: 'Roboto', sans-serif;
  font-size: 32px;
  margin-top: 20px;
  font-weight: 600;
}

.icon-description {
  color: #16325c;
  font-family: 'Roboto', sans-serif;
  font-size: 26px;
  margin-top: 32px;
  font-weight: 500;
}

.icons-container {
  display: flex;
  flex-direction: row;
  margin-bottom: 60px;
}
