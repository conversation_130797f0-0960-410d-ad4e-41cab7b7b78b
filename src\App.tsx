import './App.css';
import { Routes, Route, BrowserRouter } from 'react-router-dom';
import Header from './pages/LandingPage/Header';
import LoginHeader from './pages/LandingPage/LoginHeader';
import BackgroundClouds from './pages/LandingPage/BackgroundClouds';
// import Benefits from './components/Benefits/Benefits';
import Footer from './components/Footer';
import LessonCardSectionOne from './components/LessonCardSections/LessonCardSectionOne';
import LessonCardSectionTwo from './components/LessonCardSections/LessonCardSectionTwo';
import LandingPage from './pages/LandingPage/LandingPage';
import LoginPage from './pages/Login/LoginPage';
import InitialSignupPage from './pages/Signup/InitialSignupPage';
import SignupPageAbove14 from './pages/Signup/SignupPageAbove14';
import SignupPageBelow13 from './pages/Signup/SignupPageBelow13';
import SignupConfirmation1 from './pages/Signup/SignupConfirmation1';
import ChatBotHomePage from './pages/AiPages/chatBotHomePage';
import ChatBotPage from './pages/AiPages/ChatBotPage';
import SignupConfirmation2 from './pages/Signup/SignupConfirmation2';
import { useUser } from './context/UserContext';

const MainHeader: React.FC = () => {
  const { userName } = useUser();

  if (userName === 'Guest' || !userName) {
    // return <LoginHeader />;
    return <Header navLinkText='Chatbot' />;
  } else {
    return <LoginHeader navLinkText='Chatbot' />;
  }
};

const HomePage: React.FC = () => {
  const { userName } = useUser();

  return (
    <div>
      <h1>Welcome {userName}</h1>
      <MainHeader />
      <BackgroundClouds />
      <LandingPage />
      <div className='home-chatbot-container'>
        <ChatBotHomePage isHomePage={true} showWelcomeMessage={true} />
      </div>
      {/* <Benefits /> */}
      <LessonCardSectionOne />
      <LessonCardSectionTwo />
      <Footer />
    </div>
  );
};

const App: React.FC = () => {
  return (
    <BrowserRouter>
      <Routes>
        <Route path='/' element={<HomePage />} />
        <Route path='/login' element={<LoginPage />} />
        <Route path='/chatbot' element={<ChatBotPage />} />
        <Route
          path='/signup'
          element={<InitialSignupPage updateDestination={() => {}} />}
        />
        <Route path='/signup/above14' element={<SignupPageAbove14 />} />
        <Route path='/signup/below13' element={<SignupPageBelow13 />} />
        <Route path='/signup/confirmation1' element={<SignupConfirmation1 />} />
        <Route path='/signup/confirmation2' element={<SignupConfirmation2 />} />
      </Routes>
    </BrowserRouter>
  );
};

export default App;
