.UserForm-section {
  display: flex;
  align-items: center;
  position: relative;
  flex-wrap: wrap;
}

.Input-wrapper {
  display: flex;
  flex: 1 1 250px;
  min-width: 250px;
  flex-direction: column;
  align-items: center;
  max-width: 368.71px;
}

.Input-title {
  width: 100%;
  height: 92px;
  padding: 12px 24px;
  text-align: left;
  align-items: center;
  font-size: 33.18px;
  font-weight: 400;
  color: black;
}

.required-text {
  color: red;
}

.Input-field {
  width: 100%;
  height: 92px;
  padding: 12px 24px;
  font-size: 32px;
  font-weight: 400;
  line-height: 38.73px;
  text-align: left;
  background-color: #ffffff;
  color: #000;
  border-radius: 15px;
  border: 1.84px solid #000;
  box-sizing: border-box;
  margin-bottom: 10px;
}

.Input-field:focus {
  outline: none;
  border-color: #000;
}

.Input-instruction {
  font-size: 16px;
  color: #666;
  text-align: center;
}

.Checkbox-wrapper {
  display: flex;
  justify-content: center;
}

.checkbox {
  display: flex;
  width: 23px;
  height: 23px;
  margin-right: 10px;
  color: #ffffff;
  border: 4px solid #fc9826;
  cursor: pointer;
}

.Checkbox-label {
  font-size: 16px;
  color: #ffffff;
}

.Checkbox-label a {
  color: #fc9826;
  text-decoration: none;
}

.Checkbox-label a:hover {
  text-decoration: underline;
}

.redhere {
  color: #c01e1e;
  text-decoration: underline;
}

.redhere a:hover {
  color: #535bf2;
}
