import {
  useRef,
  useState,
  useEffect,
  AnchorHTMLAttributes,
  DetailedHTMLProps,
} from 'react';
import { useNavigate } from 'react-router-dom';

import ReactMarkdown from 'react-markdown';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import axios from 'axios';
// import '../index.css';
// import './ChatBotPage.css';
import './chatBotHomePage.css';
import 'katex/dist/katex.min.css';
import { v4 as uuidv4 } from 'uuid';
import { ReactComponent as Dog } from '../../assets/dog.svg';
import Header from '../LandingPage/Header';

interface ChatBotHomePageProps {
  showHeader?: boolean;
  showTitle?: boolean;
  showWelcomeMessage?: boolean;
  isHomePage?: boolean;
  initialQuestion?: string;
  // location: string | null; // Add this to access location state
}

const ChatBotHomePage: React.FC<ChatBotHomePageProps> = ({
  showHeader = false,
  showTitle = true,
  isHomePage = false,
  initialQuestion = '',
}) => {
  const navigate = useNavigate();
  const [botTextArea, setBotTextArea] = useState<string>(initialQuestion);
  const [botResponse, setBotResponse] = useState<string>('');
  const [chatHistory, setChatHistory] = useState<
    { type: string; text: string }[]
  >([]);
  const [loadingOpacity, setLoadingOpacity] = useState(0);
  const [threadId, setThreadId] = useState('');
  const scrollTargetRef = useRef<HTMLDivElement>(null);
  const intructionsRef = useRef<HTMLDivElement>(null);
  const [shouldScroll, setShouldScroll] = useState(false);
  const [dailyUses, setDailyUses] = useState<number>(0);
  const [dailyLimit, setDailyLimit] = useState<number>(10);

  function generateThreadId() {
    return uuidv4();
  }

  useEffect(() => {
    window.scrollTo(0, 0);

    const fetchDailyLimit = async () => {
      try {
        const response = await axios.get<{ dailyLimit: number }>(
          `${import.meta.env.VITE_APP_BASE_URL}/api/chat/dailylimit`
        );
        setDailyLimit(response.data.dailyLimit);
      } catch (error) {
        console.error('Error fetching daily limit:', error);
      }
    };

    fetchDailyLimit();

    const fetchDailyUses = async () => {
      try {
        const response = await axios.get<{ dailyUses: number }>(
          `${import.meta.env.VITE_APP_BASE_URL}/api/chat/dailyuses`
        );
        setDailyUses(response.data.dailyUses);
        console.log(response.data);
      } catch (error) {
        console.error('Error fetching daily limit:', error);
      }
    };

    fetchDailyUses();

    if (!threadId) setThreadId(generateThreadId());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Only run once on mount

  useEffect(() => {
    if (threadId) {
      // If we're not on the home page
      if (!isHomePage) {
        // Get state from location if available
        // const locationState = location?.state;
        // const showGreeting = locationState?.showGreeting;

        // console.log("Location state:", locationState);
        // console.log("Show greeting flag:", showGreeting);

        // Initialize chat history
        const initialHistory = [];

        // Add initial question if provided and it's a new question from home page
        if (initialQuestion && initialQuestion.trim() !== '') {
          initialHistory.push({ type: 'user', text: initialQuestion });

          // Set the chat history
          setChatHistory(initialHistory);

          // Process the initial question - ONLY ONCE
          console.log('Processing initial question:', initialQuestion);
          setBotTextArea('');

          // Add a small delay to ensure the component is fully mounted
          setTimeout(() => {
            // Call API without adding to history again
            fetchBotResponse(initialQuestion);
            scrollToTarget();
          }, 100);
        }
      }
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [threadId]);

  async function fetchBotResponse(questionText: string) {
    setLoadingOpacity(1);

    fetch(`${import.meta.env.VITE_APP_BASE_URL}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ input: questionText, threadId: threadId }),
    })
      .then(async (response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        if (response.body) {
          const jsonResponse = await response.json();

          setDailyUses(jsonResponse.dailyRequests);

          handleStream(jsonResponse.text);
        }
      })
      .catch((error) => {
        console.error('Error sending message:', error);
        setLoadingOpacity(0);
      });
  }

  function scrollToTarget() {
    if (scrollTargetRef.current) {
      scrollTargetRef.current?.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }

  async function sendBotMessage(questionText = botTextArea) {
    if (isHomePage) {
      // Redirect to chatbot page with the question and a flag to show greeting
      console.log('Navigating to chatbot page with:', questionText);
      navigate('/chatbot', {
        state: {
          question: questionText,
          showGreeting: true,
          isNewQuestion: true,
        },
      });
      return;
    }

    // Don't proceed if there's no text
    if (!questionText.trim()) {
      return;
    }

    // Only add to chat history if it's not already there (prevents duplication)
    const isDuplicate = chatHistory.some(
      (item) => item.type === 'user' && item.text === questionText
    );

    if (!isDuplicate) {
      console.log('Adding user message to chat history:', questionText);
      setChatHistory((prev) => [...prev, { type: 'user', text: questionText }]);
      setShouldScroll(true);
    } else {
      console.log('Skipping duplicate message:', questionText);
    }
    setBotTextArea('');

    // Use the separate function for API call

    fetchBotResponse(questionText);

    // Clear the textarea after sending
  }

  useEffect(() => {
    if (shouldScroll) {
      scrollToTarget();
      setShouldScroll(false);
    }
  }, [shouldScroll]);

  async function handleStream(response: string) {
    let index = 0;
    let tempContent = '';

    streamText(response);

    function streamText(text: string) {
      setLoadingOpacity(0);

      if (index < text.length) {
        const chunk = text.substring(index, index + 10);
        tempContent += chunk; //add chunk to temporary Content
        setBotResponse(
          tempContent
            .replace(/^["']|["']$/g, '')
            .replace(/\\n/g, '\n')
            .replace(/\\\\/g, '\\')
            .replace(/\$\$/g, '$')
        );
        index += 10;
        setTimeout(() => {
          streamText(text);
        }, 50);
      } else {
        setBotResponse('');
        setChatHistory((prev) => [
          ...prev,
          {
            type: 'bot',
            text: text
              .replace(/^["']|["']$/g, '')
              .replace(/\\n/g, '\n')
              .replace(/\\\\/g, '\\')
              .replace(/\$\$/g, '$'),
          },
        ]);
      }
    }
  }

  function handleBotTextAreaChange(
    event: React.ChangeEvent<HTMLTextAreaElement>
  ) {
    setBotTextArea(event.target.value);
  }

  const components = {
    a: (
      props: DetailedHTMLProps<
        AnchorHTMLAttributes<HTMLAnchorElement>,
        HTMLAnchorElement
      >
    ) => {
      const isHttpLink =
        props.href &&
        (props.href.startsWith('http://') || props.href.startsWith('https://'));
      return (
        <a
          {...props}
          target='_blank'
          rel='noopener noreferrer'
          className={isHttpLink ? 'http-link' : 'default-link'}
        >
          {props.children}
        </a>
      );
    },
  };

  // css for the bot's answer
  const botStyle = {
    backgroundColor: '#D9F2FF',
    marginBottom: '15px', // Add margin between messages
    borderRadius: '10px',
    color: 'black',
    padding: '12px 15px', // Increase padding all around
    fontWeight: '600',
    fontFamily: "'Inter', sans-serif",
    width: '100%',
    fontSize: '18px',
  };

  //css for the user's question
  const userStyle = {
    backgroundColor: '#FAF0FF',
    // backgroundColor: "purple",
    marginTop: '20px',
    // marginRight: '300px',
    marginBottom: '15px', // Add margin between messages
    borderRadius: '10px',
    color: '#333',
    padding: '12px 15px', // Increase padding all around
    fontWeight: '700',
    fontFamily: "'Inter', sans-serif",
    fontSize: '18px',
    //marginRight: '200px',  This will push user messages to the right
    width: 'fit-content', // Adjust width to fit content

    // alignSelf: 'right', Align to the right if using flexbox
    marginLeft: 'auto', // Push to the right
  };

  return (
    <div className='chatbot-container'>
      {showHeader && <Header />}
      <div className='dog-container'>
        <Dog style={{ paddingTop: '10px' }} />
      </div>
      {showTitle && (
        <h2
          style={{
            color: '#16325C',
            fontFamily: "'Inter', sans-serif",
            fontSize: 'clamp(20px, 4vw, 48px)',
            fontWeight: '700',
            paddingBottom: '30px',
          }}
        >
          Chat with Me, Let's Learn!
        </h2>
      )}
      <div
        className='chat-instructions'
        ref={intructionsRef}
        style={{
          textAlign: 'left',
          width: '800px',
          margin: '0 auto',
          padding: '30px',
          // backgroundColor: '#f4f1ff',
          // border: "1px solid pink",
          borderRadius: '30px',
          boxShadow: '0 2px 5px rgba(0, 0, 0, 0.1)',
          lineHeight: '1.7',
        }}
      >
        <p style={{ marginTop: 0 }}>🌟 Welcome, Explorer! 🌟</p>
        <p>
          🚫 No bad words, inappropriate stuff, or links here! We only keep
          things fun and safe. 🌈
        </p>
        <p>
          🔒 Your conversations are never recorded with your name, and we never
          ask for any personal info! 😊
        </p>

        <p>
          📝 Ready to chat? Start by typing "Hi" 👋 or "Woof" 🐱 and we can talk
          about anything! Whatever we chat about stays secret—it's never saved!
          🤫
        </p>

        <p style={{ marginBottom: 0 }}>
          🚀 If you're a paid member,{' '}
          <a onClick={() => navigate('/login')}>click here</a> to login! 🔑
        </p>
      </div>
      {chatHistory.map((element, index) => {
        const style = element.type === 'bot' ? botStyle : userStyle;
        return (
          <>
            <div
              key={index}
              style={style}
              ref={
                index >= chatHistory.length - 2 && element.type !== 'bot'
                  ? scrollTargetRef
                  : undefined
              }
            >
              <ReactMarkdown
                remarkPlugins={[remarkMath]}
                rehypePlugins={[rehypeKatex]}
                components={components}
              >
                {element.text}
              </ReactMarkdown>
            </div>
            <div style={{ height: '1px' }}></div>
          </>
        );
      })}
      {botResponse ? (
        <>
          <div style={botStyle}>
            <ReactMarkdown
              remarkPlugins={[remarkMath]}
              rehypePlugins={[rehypeKatex]}
              components={components}
            >
              {botResponse}
            </ReactMarkdown>
          </div>
        </>
      ) : null}

      <div className='loading-container'>
        <div className='loading-symbol' style={{ opacity: loadingOpacity }}>
          🌀
        </div>
      </div>
      {/* spacer */}
      <div style={{ height: isHomePage ? '10px' : `100px` }} />
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          width: '100%',
        }}
      ></div>

      <textarea
        id='bot-chat'
        value={botTextArea}
        onChange={handleBotTextAreaChange}
        placeholder={`What do you want to learn today? You may ask up to ${dailyLimit - dailyUses} more questions today.`}
        style={
          !isHomePage
            ? {
                position: 'fixed',
                marginBottom: '20px',
                // width: '800px',
                // marginLeft: '58px',
                // marginRight: '58px',
                bottom: '60px',
              }
            : { width: '800px' }
        }
      ></textarea>

      <button
        id='send-message-button'
        onClick={() => sendBotMessage()}
        style={
          !isHomePage
            ? {
                display: 'block',
                // margin: '0 auto',
                position: 'fixed',
                marginBottom: 0,

                width: '150px',
              }
            : undefined
        }
      >
        {isHomePage ? 'Go Chat' : `Reply`}
      </button>
      <br></br>
    </div>
  );
};

export default ChatBotHomePage;
