/* eslint-disable react-refresh/only-export-components */

import React from 'react';

import { ReactComponent as FacebookIcon } from '../assets/facebook--negative.svg';
import { ReactComponent as TwitterIcon } from '../assets/twitter--negative.svg';
import { ReactComponent as InstagramIcon } from '../assets/instagram--negative.svg';
import { ReactComponent as LinkedInIcon } from '../assets/linkedin--negative.svg';
import { ReactComponent as EmailIcon } from '../assets/mail-outline.svg';

interface FooterLink {
  href: string;
  text: string;
}

interface SocialIcon {
  href: string;
  icon: JSX.Element;
  id: string;
}

export const footerLinks: FooterLink[] = [
  { href: '#', text: 'Home' },
  { href: '#', text: 'Profile' },
  { href: 'https://www.bridgingtech.org/contact.html', text: 'Contact Us' },
  { href: 'https://www.bridgingtech.org/giving.html', text: 'Donate' },
  { href: '#', text: 'Volunteer' },
  { href: '#', text: 'Privacy Policy' },
];

export const socialIcons: SocialIcon[] = [
  {
    href: 'https://www.facebook.com/bridgingtechcharity/',
    icon: <FacebookIcon />,
    id: 'facebook',
  },
  {
    href: 'https://twitter.com/BridgingTech_',
    icon: <TwitterIcon />,
    id: 'twitter',
  },
  {
    href: 'https://www.instagram.com/bridgingtech/',
    icon: <InstagramIcon />,
    id: 'instagram',
  },
  {
    href: 'https://www.linkedin.com/company/bridgingtech/',
    icon: <LinkedInIcon />,
    id: 'linkedin',
  },
  {
    href: 'mailto:"<EMAIL>"',
    icon: <EmailIcon />,
    id: 'email',
  },
];
const Footer: React.FC = () => {
  return (
    <footer
      role='contentinfo'
      className='max-w-screen-sm m-auto flex-col mt-60'
    >
      <div
        role='navigation'
        className='py-footerRows flex flex-wrap flex-row justify-center'
        style={{
          padding: '100px 0px 10px ',
        }}
      >
        <div className='flex flex-row flex-nowrap justify-center'>
          {footerLinks.slice(0, 4).map((link, i) => (
            <p
              key={i}
              className='px-footerIcons font-kollektif text-sm text-black min-w-max'
            >
              <a href={link.href}>{link.text}</a>
            </p>
          ))}
        </div>
        <div className='flex flex-row justify-center'>
          {footerLinks.slice(4).map((link, i) => (
            <p
              key={i + 4}
              className='px-footerIcons font-kollektif text-sm text-black'
            >
              <a href={link.href}>{link.text}</a>
            </p>
          ))}
        </div>
      </div>

      <div
        role='navigation'
        className='py-footerRows flex flex-row flex-nowrap items-center justify-center'
        style={{
          padding: '30px 20px 100px ',
        }}
      >
        {socialIcons.map((icon, i) => {
          return (
            <a
              key={i}
              href={icon.href}
              data-testid={`${icon.id}`}
              className='px-footerIcons'
            >
              {icon.icon}
            </a>
          );
        })}
      </div>
    </footer>
  );
};

export default Footer;
