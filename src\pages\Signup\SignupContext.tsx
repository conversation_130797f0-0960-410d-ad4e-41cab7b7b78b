import React, { createContext, ReactNode, useState } from 'react';

interface SignupContextProps {
  age: number | null;
  setAge: React.Dispatch<React.SetStateAction<number | null>>;
  currentStep: number;
  setCurrentStep: React.Dispatch<React.SetStateAction<number>>;
}

const SignupContext = createContext<SignupContextProps | undefined>(undefined);

interface SignupProviderProps {
  children: ReactNode;
}

export const SignupProvider: React.FC<SignupProviderProps> = ({ children }) => {
  const [age, setAge] = useState<number | null>(null);
  const [currentStep, setCurrentStep] = useState<number>(1);

  return (
    <SignupContext.Provider
      value={{ age, setAge, currentStep, setCurrentStep }}
    >
      {children}
    </SignupContext.Provider>
  );
};

export type { SignupContextProps };
export default SignupContext;
