/* chatbot homepage css */
/* Add these styles to center the dog SVG specifically */
.chatbot-container svg {
  display: block;
  margin: 0 auto;
  /* max-width: 35%;
  min-width: 30%; */
  height: auto;
}

.chatbot-page {
  width: 100%;
  min-height: 1000px;
  position: absolute;
  left: 0px;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-color: rgb(205, 235, 251);
  z-index: -1;
  align-items: center;
  display: flex;
  flex-direction: column;
  /* overflow-y: auto; */
  width: 100%;
}

/* Make the entire chatbot section centered */
.dog-container {
  display: flex;
  justify-content: center;
  width: 100%;
  margin: 0 auto;
  padding-top: 80px;
  max-width: 48%;
  min-width: 40%;
}

/* Contained background styles */
.chatbot-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  max-width: 800px;
  height: 100%;
  margin: auto;

  /* background-color: rgb(220, 225, 255);  */
  /* background-color: pink; */
  /* margin-bottom: 100px; */
}

.chatbot-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.contained-background {
  width: 100%;
  height: 5%;
  /* background-color: #e1f4ff; */
  opacity: 0.7;
  border-radius: 20px;
}

/* Make sure the chat content is positioned properly */
.chat-messages-container {
  position: relative;
  z-index: 1;
  background-color: rgba(128, 44, 44, 0.5);
  border-radius: 15px;
  margin: 20px 0;
  padding: 15px;
}

.chat-instructions a {
  color: #2177f7;
  /* text-decoration: underline; */
  font-weight: 800;
}

.http-link {
  color: #0d61df;
  font-weight: bold;
}

.http-link:hover {
  color: #7033ff;
  text-decoration: underline;
}

.default-link {
  color: #0d61df;
  font-weight: bold;
  text-decoration: underline;
}

.default-link:hover {
  color: #7033ff;
  text-decoration: underline;
}
