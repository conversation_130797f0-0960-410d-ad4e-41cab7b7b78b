# BridgingTech-CodeTL

This work is between the Code TL apprentices and Bridging Tech.

Stack:

- Typescript
- Vite
- React
- Node

- PostgreSQL

vite and react (frontend), (make sure you have node v18+ installed in order to use vite)
node v18+ (backend)
PostgreSQL (database)

if you are cloning this repo for ths 1st time, you will need to:

- run "npm install"

to run the vite + react app for dev purposes, use:

- "npm run dev"

Backend Basic Architecure

- projects that was referred to build this architecure -
  https://github.com/remult/remult/tree/master/examples/react-todo

- along with main server , even the backend server should be started by using this command below

  `npm run dev-node`

- Please add `PORT` & `DATABASE_URL` env variable in a file called .env in root of project (similar to .gitignore)
  and use appropriate values,
  PORT stands for which port you want to use eg:3002 , database_url is the connection string for your postgresql db

Integration between Backend & Frontend

- Please add env var called `VITE_APP_BASE_URL` in .env file in root of the project, with value equal to url of backend
  eg: if you are starting the backend server locally its value would be something like "http://localhost:PORT"
  once the application is hosted its value would something like "https://bridgingtechbackend.com"
- You can use axios to communicate with backend api - refer Login.tsx on how to make API call.
  make sure to submit request with parameter names that the endpoint expects including case of parameters
  eg: Login endpoint expects {username,password,user_email}

Logging Architecture

- I am making use of well known package called winston to do the logging.
  Install Packages that are required for this to work with cmd below

  `npm instal winston winston-daily-rotate-file @types/morgan tsx`

- Use the logger in this format
  Logger.{info/debug/error/warn/http}(`{message that needs to be logged}`);
  example code written in items.router.ts file under /server
- More info on how to use this package
  https://betterstack.com/community/guides/logging/how-to-install-setup-and-use-winston-and-morgan-to-log-node-js-applications/
