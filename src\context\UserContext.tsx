import {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from 'react';
import { supabase } from '../services/supabase/supabaseClient';
interface UserContextType {
  userName: string | null;
  setUserName: (name: string | null) => void;
  pointsTotal: number | null;
  setPointsTotal: (points: number | null) => void;
  loginStreak: number | null;
  setLoginStreak: (streak: number | null) => void;
}

const UserContext = createContext<UserContextType>({
  userName: null,
  setUserName: () => {},
  pointsTotal: null,
  setPointsTotal: () => {},
  loginStreak: null,
  setLoginStreak: () => {},
});

export const UserProvider = ({ children }: { children: ReactNode }) => {
  const [userName, setUserName] = useState<string | null>(null);
  const [pointsTotal, setPointsTotal] = useState<number | null>(null);
  const [loginStreak, setLoginStreak] = useState<number | null>(null);

  useEffect(() => {
    // Check for existing session on load
    const getSession = async () => {
      try {
        // Get current session
        const {
          data: { session },
        } = await supabase.auth.getSession();

        if (session) {
          // Get user data
          const {
            data: { user },
          } = await supabase.auth.getUser();

          if (user) {
            const name = user.user_metadata.username || 'Guest';
            setUserName(name);

            // Fetch user points data
            const { data: userData } = await supabase
              .from('users')
              .select('user_id')
              .eq('auth_id', user.id)
              .single();

            if (userData) {
              const { data: pointsData } = await supabase
                .from('user_points_history')
                .select('points_total, login_streak')
                .eq('user_id', userData.user_id)
                .single();

              if (pointsData) {
                setPointsTotal(pointsData.points_total);
                setLoginStreak(pointsData.login_streak);
              }
            }
          }
        }
      } catch (error) {
        console.error('Error loading user session:', error);
      }
    };

    getSession();

    // Set up auth state change listener
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event) => {
      if (event === 'SIGNED_OUT') {
        setUserName(null);
        setPointsTotal(null);
        setLoginStreak(null);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return (
    <UserContext.Provider
      value={{
        userName,
        setUserName,
        pointsTotal,
        setPointsTotal,
        loginStreak,
        setLoginStreak,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
