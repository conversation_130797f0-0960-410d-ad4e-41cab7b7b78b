.split-screen-container {
  display: flex;
  height: 100vh;
}

.image-container {
  flex: 1;
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.sign-up-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin: auto;
  justify-content: center;
  align-items: center;

  width: auto;
  height: auto;
  padding: auto;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 9px;
  width: 100%;
  margin-top: 30px;
}

.text {
  color: black;
  font-size: 30px;
  font-weight: 800;
}

.inputs {
  margin-top: 55px;
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.input {
  display: flex;
  align-items: center;
  margin: auto;
  width: 480px;
  height: 50px;
  background: lightgrey;
  border-radius: 10px;
}

.input input {
  height: 50px;
  width: 400px;
  background: transparent;
  border: none;
  outline: none;
  color: black;
  font-size: 19px;
}

.submit-container {
  display: flex;
  gap: 30px;
  margin: 60px auto;
}

.submit {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 480px;
  height: 50px;
  color: #fff;
  background: black;
  border-radius: 10px;
  font-size: 25px;
  font-weight: 700;
  cursor: pointer;
}

.specific-sign-up-inputs-container {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
