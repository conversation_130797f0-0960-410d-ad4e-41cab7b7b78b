// This page is used to display the chatbot in a standalone page.
import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import Header from '../LandingPage/Header';
import Footer from '../../components/Footer';
import ChatBotHomePage from '../AiPages/chatBotHomePage';
import './ChatBotPage.css'; // Add a CSS file for styling
import { useUser } from '../../context/UserContext';
import LoginHeader from '../LandingPage/LoginHeader';

const MainHeader: React.FC = () => {
  const { userName } = useUser();

  if (userName === 'Guest' || !userName) {
    // return <LoginHeader />;
    return <Header navLinkText='Home Page' />;
  } else {
    return <LoginHeader navLinkText='Home Page' />;
  }
};

const ChatBotPage: React.FC = () => {
  const location = useLocation();
  const initialQuestion = location.state?.question || '';
  const showGreeting = location.state?.showGreeting || false;

  // Log the initial question for debugging
  useEffect(() => {
    console.log('ChatBotPage - Initial question:', initialQuestion);
    console.log('ChatBotPage - Show greeting:', showGreeting);
  }, [initialQuestion, showGreeting]);

  return (
    <>
      <MainHeader />
      <div className='chatbot-page'>
        <ChatBotHomePage
          showHeader={false}
          showTitle={true}
          showWelcomeMessage={false}
          isHomePage={false}
          initialQuestion={initialQuestion}
        />
        <Footer />
      </div>
    </>
  );
};

export default ChatBotPage;
