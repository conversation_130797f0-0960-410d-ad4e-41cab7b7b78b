import React, { useState } from 'react';
import './UserForm.css';

interface UserFormProps {
  onFormSubmit: (
    email: string,
    username: string,
    password: string,
    termsChecked: boolean
  ) => void;
}

const UserForm: React.FC<UserFormProps> = ({ onFormSubmit }) => {
  const [email, setEmail] = useState<string>('');
  const [username, setUsername] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [termsChecked, setTermsChecked] = useState<boolean>(false);

  const handleInputChange =
    (setter: React.Dispatch<React.SetStateAction<string>>) =>
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setter(event.target.value);
    };

  const handleCheckboxChange = () => {
    setTermsChecked(!termsChecked);
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    onFormSubmit(email, username, password, termsChecked);
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className='UserForm-section'>
        <div className='Input-wrapper'>
          <div className='Input-title'>Your email</div>
          <input
            type='email'
            className='Input-field'
            value={email}
            onChange={handleInputChange(setEmail)}
            required
          />
          <div className='Input-instruction'>
            Need advice on creating an email? Click{' '}
            <a href='' className='redhere'>
              here
            </a>
          </div>
        </div>

        <div className='Input-wrapper'>
          <div className='Input-title'>Create a username</div>
          <input
            type='text'
            className='Input-field'
            value={username}
            onChange={handleInputChange(setUsername)}
            required
          />
          <div className='Input-instruction'>
            For privacy & safety, don’t use your real name. Use letters &
            numbers only.
          </div>
        </div>

        <div className='Input-wrapper'>
          <div className='Input-title'>Create a password</div>
          <input
            type='password'
            className='Input-field'
            value={password}
            onChange={handleInputChange(setPassword)}
            required
          />
          <div className='Input-instruction'>
            Passwords should be at least 8 characters long. Use letters, numbers
            and other characters.
          </div>
        </div>
      </div>

      <div className='Checkbox-wrapper'>
        <input
          type='checkbox'
          className='Checkbox'
          checked={termsChecked}
          onChange={handleCheckboxChange}
          required
        />
        <label className='Checkbox-label'>
          By creating an account, you agree to the{' '}
          <a href='/terms'>Terms of Services</a> and{' '}
          <a href='/privacy'>Privacy Policy</a>.
        </label>
      </div>
      <button
        type='submit'
        style={{
          width: '296.94px',
          height: '61.63px',
          backgroundColor: '#0095DF',
          color: '#FFFFFF',
          borderRadius: '25px',
          padding: '12px 24px',
          fontSize: '18px',
          border: 'none',
          cursor: 'pointer',
        }}
        aria-label={`Go to ${'Submit'}`}
      >
        Submit
      </button>
    </form>
  );
};

export default UserForm;
