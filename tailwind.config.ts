import type { Config } from 'tailwindcss';

module.exports = {
  content: [
    './src/App.{ts,tsx}',
    './src/components/*.{ts,tsx}',
    './src/components/**/*.{ts,tsx}',
  ],
  theme: {
     boxShadow: {
      DEFAULT: '0 3px 0 #57a300',
      teal: '0 3px 0 #0F766E',
      yellow: '0 3px 0 #E49E00',
      blueGray: '0 3px 0 #475569',
      none: 'none',
    },
    extend: {
      colors: {
        'bright-blue': '#43B6FE',
        'light-blue': '#CEEBFA',
        peach: '#F7C36D',
        orange: '#FC9826',
        white: '#FFFFFF',
        black: '#000000',
      },
      fontFamily: {
        kollektif: ['Kollektif', 'sans-serif'],
        aileron: ['Aileron', 'sans-serif'],
      },
      padding: {
        footerIcons: '12px',
        footerRows: '16px',
      },
    },
  },
  plugins: [],
} satisfies Config;
