import React, { useState, useEffect } from 'react';
import './BirthdateDropdown.css';

type OptionType = {
  label: string;
  value: string;
};

type BirthdateDropdownProps = {
  onDateChange?: (dateOfBirth: string) => void; // Optional prop for callback
};

const BirthdateDropdown: React.FC<BirthdateDropdownProps> = ({
  onDateChange,
}) => {
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const [selectedMonth, setSelectedMonth] = useState<string | null>(null);
  const [selectedDay, setSelectedDay] = useState<string | null>(null);
  const [selectedYear, setSelectedYear] = useState<string | null>(null);

  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];
  const days = Array.from({ length: 31 }, (_, i) => ({
    label: String(i + 1),
    value: String(i + 1),
  }));
  const years = Array.from({ length: 50 }, (_, i) => ({
    label: String(new Date().getFullYear() - i),
    value: String(new Date().getFullYear() - i),
  }));

  const handleSelect = (dropdown: string, option: OptionType) => {
    console.log(`${dropdown}: ${option.value}`);
    setOpenDropdown(null);

    switch (dropdown) {
      case 'month':
        setSelectedMonth(months[parseInt(option.value) - 1]);
        break;
      case 'day':
        setSelectedDay(option.label);
        break;
      case 'year':
        setSelectedYear(option.label);
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (
      selectedMonth !== null &&
      selectedDay !== null &&
      selectedYear !== null
    ) {
      const dateString = `${selectedMonth} ${selectedDay}, ${selectedYear}`;
      console.log('Selected Date:', dateString);
      onDateChange?.(dateString); // Call the callback function with the date string
    }
  }, [selectedMonth, selectedDay, selectedYear, onDateChange]);

  return (
    <div className='Dropdown-section'>
      <div className='Dropdown-button-wrapper'>
        <button
          className='Dropdown-button MonthButton'
          onClick={() =>
            setOpenDropdown(openDropdown === 'month' ? null : 'month')
          }
        >
          <span className='Dropdown-text'>
            {selectedMonth ? selectedMonth : 'Month'}
          </span>
        </button>
        {openDropdown === 'month' && (
          <ul className='Dropdown-list'>
            {months.map((month, index) => (
              <li
                key={index + 1}
                className='Dropdown-item'
                onClick={() =>
                  handleSelect('month', {
                    label: month,
                    value: String(index + 1),
                  })
                }
              >
                {month}
              </li>
            ))}
          </ul>
        )}
      </div>

      <div className='Dropdown-button-wrapper'>
        <button
          className='Dropdown-button DayButton'
          onClick={() => setOpenDropdown(openDropdown === 'day' ? null : 'day')}
        >
          <span className='Dropdown-text'>
            {selectedDay ? selectedDay : 'Day'}
          </span>
        </button>
        {openDropdown === 'day' && (
          <ul className='Dropdown-list'>
            {days.map((option) => (
              <li
                key={option.value}
                className='Dropdown-item'
                onClick={() => handleSelect('day', option)}
              >
                {option.label}
              </li>
            ))}
          </ul>
        )}
      </div>

      <div className='Dropdown-button-wrapper'>
        <button
          className='Dropdown-button YearButton'
          onClick={() =>
            setOpenDropdown(openDropdown === 'year' ? null : 'year')
          }
        >
          <span className='Dropdown-text'>
            {selectedYear ? selectedYear : 'Year'}
          </span>
        </button>
        {openDropdown === 'year' && (
          <ul className='Dropdown-list'>
            {years.map((option) => (
              <li
                key={option.value}
                className='Dropdown-item'
                onClick={() => handleSelect('year', option)}
              >
                {option.label}
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export default BirthdateDropdown;
