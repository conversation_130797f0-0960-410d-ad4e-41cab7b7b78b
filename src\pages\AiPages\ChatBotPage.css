h2 {
  text-align: center;
  font-size: 20px;
}

/* a {
  color: #475dca;
  
  font-weight: 800;
} */

.chat-instructions a:hover {
  color: #5932b4;
  text-decoration: underline;
}

/* .chatbot-page-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
} */

.chatbot-background-container {
  width: 100%;
  /* max-width: 1200px; */
  margin: 0 auto;
  /* background-size: cover;  */
  /* background-color: #cdebfb; */
  /* border-radius: 20px; */
  /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); */
  overflow: hidden;
  position: relative;
}

#send-message-button {
  /* margin-top: 30px; */
  /* min-width: 114px; */
  width: 120px;
  height: 50px;
  background-color: #006dcc;
  display: inline-block;
  color: #ffffff;
  font-size: 20px;
  font-weight: 700;
  font-family: 'Inter', sans-serif;
  /* padding: 0 10px; */
  border-radius: 20px;
  margin-bottom: 100px;
  bottom: 20px;
  border: none;
  cursor: pointer;
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

#send-message-button:hover {
  background-color: #6075dd;
  transform: scale(1.05);
  box-shadow:
    0 4px 15px rgba(105, 72, 196, 0.5),
    0 0 20px rgba(105, 72, 196, 0.3);
}

/* text box input area */
#bot-chat {
  font-weight: 600;
  /* background-color: pink; */
  font-family: 'Inter', sans-serif;
  color: rgb(48, 48, 48);
  font-size: 18px;
  border-radius: 20px;
  border: none;
  background-color: #f5f5f5;
  display: block;
  height: 80px;
  resize: none;
  padding: 20px;
  width: 800px;
  margin: 0 auto;
  margin-bottom: 20px;
  margin-top: -20px;
  scrollbar-width: none;
  transition: box-shadow 0.3s ease;
  box-shadow:
    0 4px 15px rgba(99, 135, 255, 0.5),
    0 0 20px rgba(202, 183, 255, 0.3);
}

#bot-chat:focus {
  outline: none;
  box-shadow:
    0 0 10px rgba(176, 141, 209, 0.8),
    0 0 20px rgba(50, 90, 102, 0.4);
  /* box-shadow:
    0 4px 15px rgba(99, 135, 255, 0.5),
    0 0 20px rgba(202, 183, 255, 0.3); */
}

.chat-instructions {
  padding: 30px;
  border-radius: 20px;
  background-color: #f4f1ff;
  /* background-color: #d2eefd; */
  /* background-color: #cdebfb; */
  color: #37474f;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  line-height: 20px;
  white-space: pre-line;
  font-weight: 700;
  margin-bottom: 20px;
}

.chat-instructions p {
  font-size: 18px;
  line-height: 1.5;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-symbol {
  font-size: 30px;
  /* background-color: green; */
  animation: rotate 1s linear infinite;
  display: inline-block;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
