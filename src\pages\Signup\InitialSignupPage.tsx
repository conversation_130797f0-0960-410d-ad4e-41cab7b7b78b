import React, { useState, useEffect } from 'react';
import { ReactComponent as InitialSignupPageBG } from '../../assets/InitialSignupPageBG.svg';
import { ReactComponent as BridgingTechLogo } from '../../assets/BridgingTechLogoTwo.svg';
import { ReactComponent as LoginStepCount } from '../../assets/loginstepcount.svg';
import NextButton from './NextButton';
import BirthdateDropdown from './BirthdateDropdown';
import './InitialSignupPage.css';

interface InitialSignupPageProps {
  updateDestination: (newDestination: string) => void;
}

const InitialSignupPage: React.FC<InitialSignupPageProps> = ({
  updateDestination,
}) => {
  const [nextDestination, setNextDestination] = useState('/');

  const handleDateChange = (dateOfBirth: string) => {
    const birthDate = new Date(dateOfBirth);
    const currentDate = new Date();
    let age = currentDate.getFullYear() - birthDate.getFullYear();
    const m = currentDate.getMonth() - birthDate.getMonth();

    if (m < 0 || (m === 0 && currentDate.getDate() < birthDate.getDate())) {
      age--;
    }

    console.log('Age:', age);
    if (age < 13) {
      setNextDestination('./below13');
    } else {
      setNextDestination('./above14');
    }
  };

  useEffect(() => {
    console.log('Next Destination: ', nextDestination);
    updateDestination(nextDestination);
  }, [nextDestination, updateDestination]);

  return (
    <div className='signup-container'>
      <BridgingTechLogo className='logo' />
      <div className='logo-text'>
        <span className='brand-text-orange'>Bridging</span>
        <span className='brand-text-blue'>Tech</span>
        <span className='brand-text-green'>Ed</span>
      </div>
      <div className='login-svg-container'>
        <InitialSignupPageBG
          preserveAspectRatio='none'
          viewBox='0 0 1920 1080'
        />
      </div>
      <LoginStepCount className='login-step-count' />
      <div className='dob-text'>
        <span className='dob-question'>What is your date of birth?&#40;</span>
        <span className='required-text'>required</span>
        <span className='dob-question'>&#41;</span>
      </div>
      <BirthdateDropdown onDateChange={handleDateChange} />
      <div className='parent-guardian-text'>
        <span>
          This is to determine whether we need to notify your parent or guardian
          about your account.{' '}
        </span>
      </div>
      <NextButton destination={nextDestination} />
    </div>
  );
};

export default InitialSignupPage;
