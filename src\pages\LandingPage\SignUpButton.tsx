// SignUpButton.tsx
import React from 'react';

const SignUpButton: React.FC = () => {
  const [isHovered, setIsHovered] = React.useState(false);
  return (
    <button
      style={{
        backgroundColor: '#FFBD4A',
        color: '#000000',

        padding: '10px 20px',
        fontSize: '20px',
        fontWeight: 'bold',
        marginLeft: '8px',
        cursor: 'pointer',
        // borderRadius: '12px',
        borderRadius: isHovered ? '0' : '12px',
        transition: 'border-radius 0.3s ease, transform 0.4s ease',
        transform: isHovered ? 'scale(1.07)' : 'scale(1)',
        textDecoration: isHovered ? 'underline' : 'none',
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      Sign Up
    </button>
  );
};

export default SignUpButton;
