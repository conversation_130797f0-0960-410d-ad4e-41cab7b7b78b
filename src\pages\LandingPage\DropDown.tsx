import React from 'react';

type DropDownOption = {
  label: string;
  action: () => void;
};

type DropDownProps = {
  options: DropDownOption[];
  showDropDown: boolean;
  toggleDropDown: () => void;
  optionSelection: (option: DropDownOption) => void;
};

const DropDown: React.FC<DropDownProps> = ({
  options,
  showDropDown,
  optionSelection,
}) => {
  return (
    <div className={showDropDown ? 'dropdown active' : 'dropdown'}>
      {options.map((option) => (
        <div
          key={option.label} // Use label as unique key
          onClick={() => optionSelection(option)} // Call optionSelection directly with option
          className='dropdown-item'
          role='button' // Add role="button" for better semantics
          tabIndex={0} // Add tabIndex for keyboard accessibility
        >
          {option.label}
        </div>
      ))}
    </div>
  );
};

export default DropDown;
