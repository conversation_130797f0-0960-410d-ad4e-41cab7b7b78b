.logo {
  position: relative;
  bottom: 20px;
  width: 86px;
  height: 62px;
}

form {
  text-align: center;
}

.logo-text {
  display: flex;
  align-items: flex-start;
  position: relative;
  font-family: Roboto;
  font-size: 24px;
  font-weight: 700;
  bottom: 65px;
  right: 20px;
}
.brand-text-orange {
  color: #ff6b00;
}

.brand-text-blue {
  color: #02aaeb;
}

.brand-text-green {
  color: #45c717;
}

.login-svg-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: -1; /* Make sure the image stays in the background */
  overflow: hidden;
}

.login-svg-container svg {
  width: 100vw;
  height: 100vh;
  object-fit: cover;
  position: relative;
}

.Bread-crumb2 {
  margin: auto;
  display: flex;
  align-items: center;
  position: relative;
}

.UserForm-section {
  display: flex;
  position: relative;
  justify-content: center;
  align-items: flex-start;
  /* gap: 20px;
  flex-wrap: nowrap; */
}

.Input-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 20px;
}

.Input-title {
  display: flex;
  justify-content: flex-start;
  font-size: 18px;
  font-weight: 600;
  color: black;
  font-family: Inter;
}

.Input-field {
  width: 100%;
  height: 92px;
  padding: 12px 24px;
  font-size: 32px;
  font-weight: 400;
  line-height: 38.73px;
  text-align: left;
  background-color: #ffffff;
  color: #000;
  border-radius: 15px;
  border: 1.84px solid #000;
  box-sizing: border-box;
  margin-bottom: 10px;
}

.Input-field:focus {
  outline: none;
  border-color: #000;
}

.Input-instruction {
  font-size: 16px;
  color: #666;
  text-align: center;
}

.Checkbox-wrapper {
  display: flex;
  align-items: center;
  margin-top: 20px;
}

.Checkbox {
  width: 23px;
  height: 23px;
  margin-right: 10px;
  background: #ffffff;
  border: 4px solid #fc9826;
  cursor: pointer;
}

.Checkbox-label {
  font-size: 16px;
  color: #000;
}

.Checkbox-label a {
  color: #fc9826;
  text-decoration: none;
}

.Checkbox-label a:hover {
  text-decoration: underline;
}
