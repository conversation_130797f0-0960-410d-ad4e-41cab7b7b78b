import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ReactComponent as Arrow } from '../../assets/arrow.svg';
import DropDown from './DropDown';
import { supabase } from '../../services/supabase/supabaseClient';
import './DropDown.css';
import { useUser } from '../../context/UserContext';

type DropDownOption = {
  label: string;
  action: () => void;
};

const ArrowDropdown: React.FC = () => {
  const [showDropDown, setShowDropDown] = useState<boolean>(false);
  const navigate = useNavigate();

  const { setUserName, setLoginStreak, setPointsTotal } = useUser();

  const toggleDropDown = () => {
    setShowDropDown(!showDropDown);
  };

  const optionSelection = (option: DropDownOption) => {
    option.action();
    setShowDropDown(false);
  };

  const handleLogout = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      alert('Unable to logout');
    }
    setUserName(null);
    setLoginStreak(null);
    setPointsTotal(null);
    navigate('/');
  };

  const options = [
    { label: 'Profile', action: () => console.log('Profile clicked') },
    { label: 'Settings', action: () => console.log('Settings clicked') },
    { label: 'Logout', action: handleLogout },
  ];

  return (
    <div style={{ position: 'relative' }}>
      <button
        onClick={toggleDropDown}
        style={{
          background: 'none',
          border: 'none',
          padding: '10px',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          transform: showDropDown ? 'rotate(180deg)' : 'rotate(0deg)',
          transition: 'transform 0.3s ease',
        }}
      >
        <Arrow
          style={{
            height: '13px',
            width: '22px',
          }}
        />
      </button>
      {showDropDown && (
        <DropDown
          options={options}
          showDropDown={showDropDown}
          toggleDropDown={toggleDropDown}
          optionSelection={optionSelection}
        />
      )}
    </div>
  );
};

export default ArrowDropdown;
