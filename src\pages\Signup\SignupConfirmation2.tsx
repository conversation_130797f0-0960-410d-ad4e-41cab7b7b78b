import React from 'react';
import { ReactComponent as InitialSignupPageBG } from '../../assets/InitialSignupPageBG.svg';
import { ReactComponent as BridgingTechLogo } from '../../assets/BridgingTechLogoTwo.svg';
import { ReactComponent as Breadcrumb3 } from '../../assets/Breadcrumb3.svg';
import { ReactComponent as CongratsBanner } from '../../assets/CongratsBanner.svg';
import HomeButton from './HomeButton';

import './SignupConfirmation2.css';

const SignupConfirmation2: React.FC = () => {
  return (
    <div>
      <BridgingTechLogo className='logo' />
      <div className='logo-text'>
        <span className='brand-text-orange'>Bridging</span>
        <span className='brand-text-blue'>Tech</span>
        <span className='brand-text-green'>Ed</span>
      </div>
      <div className='login-svg-container'>
        <InitialSignupPageBG
          preserveAspectRatio='none'
          viewBox='0 0 1920 1080'
        />
      </div>
      <Breadcrumb3 className='Bread-crumb3' />
      <CongratsBanner className='Congrats-Banner' />
      <div className='Confirmation-Text'>
        <p>Your account has been created. To begin exploring,click here.</p>
      </div>
      <HomeButton destination='/' />
    </div>
  );
};

export default SignupConfirmation2;
