{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "outDir": "./dist",
    "skipLibCheck": true,
    "types": ["vite-plugin-svgr/client", "jest", "node"],
    "esModuleInterop": true,

    /* Bundler mode */
    "moduleResolution": "node",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },

  "include": [
    "src/**/*",
    "<rootDir>/setupTests.ts",
    "<rootDir>/src/declarations.d.ts"
  ],

  "references": [{ "path": "./tsconfig.node.json" }],

  "ts-node": {
    "esm": true
  }
}
