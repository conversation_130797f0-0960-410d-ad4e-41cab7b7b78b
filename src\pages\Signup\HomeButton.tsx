import React from 'react';
import { Link } from 'react-router-dom';

interface HomeButtonProps {
  destination?: string;
}

const HomeButton: React.FC<HomeButtonProps> = ({ destination = '/' }) => {
  return (
    <div style={{ marginTop: '20px', textAlign: 'center' }}>
      <Link
        to={destination}
        style={{
          lineHeight: '29px',
          fontFamily: 'Inter, sans-serif',
          fontWeight: '600',
          fontSize: '36px',
          width: '233px',
          height: '56px',
          backgroundColor: '#FF4D00',
          color: '#FFFFFF',
          borderRadius: '12px',
          padding: '12px 24px',
          border: 'none',
          textDecoration: 'none',
          display: 'inline-block',
        }}
      >
        Home
      </Link>
    </div>
  );
};

export default HomeButton;
