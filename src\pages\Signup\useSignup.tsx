// import { useContext } from 'react';
// import SignupContext from '../Signup/SignupContext';
// import { SignupContextProps } from '../Signup/SignupContext';
// // import SignupContext, { SignupContextProps } from '../contexts/SignupContext';

// const useSignup = (): SignupContextProps => {
//   const context = useContext(SignupContext);
//   if (!context) {
//     throw new Error('useSignup must be used within a SignupProvider');
//   }
//   return context;
// };

// export default useSignup;
