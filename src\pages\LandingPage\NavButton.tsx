import React from 'react';

interface NavButtonProps {
  text?: string;
}

const NavButton: React.FC<NavButtonProps> = ({ text = '' }) => {
  const [isHovered, setIsHovered] = React.useState(false);
  return (
    <button
      style={{
        backgroundColor: '#006dcc',
        color: 'white',

        padding: '10px 20px',
        fontSize: '20px',
        fontWeight: 'bold',
        marginLeft: '8px',
        cursor: 'pointer',
        // borderRadius: '12px',
        borderRadius: isHovered ? '0' : '12px',
        transition: 'border-radius 0.3s ease, transform 0.4s ease',
        transform: isHovered ? 'scale(1.07)' : 'scale(1)',
        textDecoration: isHovered ? 'underline' : 'none',
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {text}
    </button>
  );
};

export default NavButton;
