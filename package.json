{"name": "bridgingtech-codetl", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "npx prettier -w .", "preview": "vite preview", "test": "jest", "dev-node": "tsx watch src/server"}, "dependencies": {"@supabase/supabase-js": "^2.49.1", "@svgr/cli": "^8.1.0", "@svgr/core": "^8.1.0", "@svgr/webpack": "^8.1.0", "@types/morgan": "^1.9.9", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.11.0", "autoprefixer": "^10.4.17", "axios": "^1.6.8", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "nodemailer": "^6.10.0", "nodemon": "^3.1.0", "pg": "^8.13.0", "postcss": "^8.4.35", "react": "^18.3.1", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-modal-video": "^2.0.2", "react-router-dom": "^6.26.1", "rehype-katex": "^7.0.1", "remark-math": "^6.0.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^3.4.1", "tsx": "^4.7.1", "ua-parser-js": "^1.0.37", "uuid": "^11.1.0", "winston": "^3.12.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@babel/cli": "^7.24.1", "@babel/core": "^7.24.4", "@babel/plugin-transform-modules-commonjs": "^7.24.1", "@babel/preset-env": "^7.24.4", "@babel/preset-typescript": "^7.24.1", "@svgr/webpack": "^8.1.0", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.2", "@types/bcrypt": "^5.0.2", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/dotenv": "^8.2.0", "@types/express": "^4.17.21", "@types/helmet": "^4.0.0", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20.12.4", "@types/pg": "^8.11.4", "@types/react": "^18.2.60", "@types/react-dom": "^18.2.19", "@types/react-modal-video": "^1.2.3", "@types/requirejs": "^2.1.37", "@types/ua-parser-js": "^0.7.39", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "babel-jest": "^29.7.0", "bcrypt": "^5.1.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-transformer-svg": "^2.0.2", "jsdom": "^24.0.0", "postcss-import": "^16.1.0", "prettier": "^3.2.5", "sass": "^1.77.2", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.3", "url-loader": "^4.1.1", "vite": "^5.2.8", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.3.2", "webpack-cli": "^5.1.4"}, "description": "This work is between the Code TL apprentices and Bridging Tech.", "main": "index.js", "keywords": [], "author": "", "license": "ISC", "eslintConfig": {"rules": {"no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "off"}}}