import { useNavigate } from 'react-router-dom';
import { useState } from 'react';

interface NextButtonProps {
  destination: string;
  label?: string;
  isLoading?: boolean;
}

const NextButton: React.FC<NextButtonProps> = ({
  destination = '/SignupPageBelow13',
  label = 'Next',
  isLoading = false,
}) => {
  const navigate = useNavigate();
  const [isPending, setIsPending] = useState(false);

  const handleClick = async () => {
    if (!isPending && !isLoading) {
      setIsPending(true);
      try {
        await navigate(destination);
      } catch (error) {
        console.error('Navigation failed:', error);
      } finally {
        setIsPending(false);
      }
    }
  };

  return (
    <button
      type='button'
      onClick={handleClick}
      disabled={isLoading || isPending}
      style={{
        width: '296.94px',
        height: '61.63px',
        backgroundColor: '#0095DF',
        color: '#FFFFFF',
        borderRadius: '25px',
        padding: '12px 24px',
        fontSize: '18px',
        border: 'none',
        cursor: 'pointer',
        ...(isLoading ? { opacity: 0.7 } : {}),
      }}
      aria-label={`Go to ${label}`}
    >
      {isLoading ? (
        <span role='status' aria-live='polite'>
          Loading...
        </span>
      ) : (
        <span>{label}</span>
      )}
    </button>
  );
};

export default NextButton;
