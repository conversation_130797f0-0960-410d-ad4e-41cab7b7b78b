/* container to the right */
.split-screen-container {
  display: flex;
  height: 100vh;
  flex-direction: row;
  background-color: white;
}

/* container to the left */
.left-container {
  flex: 1;
  display: flex;
  padding-bottom: 2.3rem;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 50%;

  background: linear-gradient(
    180deg,
    #f2fbff 0%,
    #cdebfb 44%,
    #75cfff 72.5%,
    #61c7fe 100%
  );
}

.image-container {
  flex: 1;
  display: flex;
  margin: 0 auto; /* center the container */
  margin-top: 4em;
  align-items: center;
  justify-content: center;
  /* background-color: pink; */
  width: 100%;
  height: 100%;
  max-width: 30em;
  max-height: 30em;
  transition: transform 0.2s;
}

.image-container:hover {
  animation: bounce 0.5s ease-in-out;
}

@keyframes bounce {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0);
  }
}

.sign-in-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  /* background-color: green; */
  /* justify-content: center; */
  align-items: flex-start;
  align-items: center;
  /* margin: auto; */
  margin: 30px auto 0 auto;
  width: auto;
  height: auto;
  padding: auto;
  width: 10vw;
  height: 80vw;
}

.header .text {
  color: #16325c;
  text-align: center;
  font-feature-settings:
    'liga' off,
    'clig' off;
  font-family: Roboto;
  font-size: 2.75rem;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  display: flex;
  /* width: 410px; */
  width: 10em;
  height: auto;
  flex-direction: column;
  justify-content: center;

  /* background-color: purple; */
}

.inputs .input-container {
  /* background-color: pink; */
  margin-top: 0px;
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.input-container label {
  padding-left: 2.1em;
}

.input-container {
  display: flex;
  flex-direction: column;

  font-size: 20px;
  color: #16325c;
  padding-bottom: 20px;
  font-family: Inter;

  font-style: normal;
  font-weight: 700;
  line-height: 0px; /* 0% */
  /* padding: 0 5% 20px 5%; */
  /* background-color: red; */
}

h1.main-title {
  color: #16325c;
  padding-bottom: 1em;
  max-width: 400px;
  margin: 0 auto; /* center the title horizontally */
  /* padding-right: 70px; */
  font-feature-settings:
    'liga' off,
    'clig' off;
  -webkit-text-stroke-width: 1;
  -webkit-text-stroke-color: #000;
  font-family: Roboto;
  /* font-size: 48px; */
  font-size: 3em;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  /* letter-spacing: .7px; */
  /* background-color: aquamarine; */
}

h2.tagline {
  color: #16325c;
  padding-bottom: 1.6em;
  max-width: 400px;
  text-align: center;
  font-feature-settings:
    'liga' off,
    'clig' off;
  font-family: Roboto;
  font-size: 30px;
  font-style: normal;
  font-weight: 500;
  line-height: 116.7%; /* 23.34px */
  /* background-color: pink; */
}

h3.privacy-policy-text {
  color: #16325c;
  max-width: 400px;
  text-align: center;
  margin: 0 auto;
  padding-bottom: 5em;
  text-align: center;
  font-feature-settings:
    'liga' off,
    'clig' off;
  font-family: Roboto;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: 116.7%; /* 23.34px */
  /* background-color: rgb(187, 105, 117); */
}

.privacy-policy-link {
  text-decoration: underline;
}

.privacy-policy-link:hover {
  color: #00505e;
}

/* Right side container */
/* ___________________________ */

.logo-container {
  /* width: 11%;
  height: 11%;
  min-width: 12%;
  min-height: 12%;
  max-width: 12%;
  max-height: 12%;
  padding-bottom: 1rem; */
  min-width: 80px;
  min-height: 80px;
  max-width: 100px;
  max-height: 10px;
  /* padding-bottom: .5rem; */
  margin-bottom: -1.1rem;
  object-fit: contain;
  transition: transform 0.2s;
  /* background-color: purple; */
}

.logo-container:hover {
  animation: spin 0.5s linear forwards;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* rectangular input box */
.input-container .input {
  background-color: #ffffff;
  max-width: 20em;
  min-width: 1em;
  /* padding: 0.5em 1em; */
  border: 0.1em solid #16325c;
  border-radius: 10px;
  /* height: 50px; */
}

.forget-password-text {
  color: #16325c;
  text-align: center;
  font-family: Inter;
  font-size: 1.2rem;
  font-style: normal;
  font-weight: 500;
  line-height: 8px;
}

.input input[type='text'] {
  color: #16325c; /* default text color */
}

.input input[type='password'] {
  /* default styles */
  color: #16325c; /* default text color */
}

.click-here-btn {
  text-decoration: underline;
  color: #16325c;
  padding-left: 0.3em;
}

.click-here-btn:hover {
  text-decoration: underline;
  color: #ff4d00;
}

.divider-break {
  display: flex;
  justify-content: center;
  align-items: center;
  /* background-color: green; */
  width: 25.7em;
  /* padding-bottom: 30px; */
}

.new-to-btech-text {
  color: #16325c;
  text-align: center;
  margin: 0 0px;
  font-weight: 500;

  /* background-color: pink; */
}

.line-behind-text {
  flex: 1; /* so the lines take up equal space on either side of the text */
  height: 0.5px;

  background-color: #16325c;
  margin: 0 10px;
}

.LoginFormButton {
  font-family: Inter, sans-serif;
  font-weight: 700;
  text-align: center;
  font-size: 0.9em;
  flex: none;
  background-color: #007bff;
  color: #ffffff;
  /* padding: 6px 10px; */
  border-radius: 10px;
  border: 0px;
  cursor: pointer;
  /* width: 400px; */
  width: 17.9em;
  height: 2.3em;
}

.LoginFormButton:hover {
  background-color: #025ec0;
}

.create-account-btn {
  /* background-color: pink; */
  font-family: Inter, sans-serif;
  font-weight: 700;
  text-align: center;
  font-size: 1.4em;
  flex: none;
  /* border: 2px solid #16325c; */
  border: 0.1em solid #16325c;
  color: #16325c;
  padding: 6px 10px;
  border-radius: 10px;
  /* border: 0px; */
  cursor: pointer;
  /* width: 400px; */
  width: 17.8em;
  height: 2.3em;
  margin-bottom: 8em;
  margin-top: 50px;
}

.create-account-btn:hover {
  background-color: #16325c;
  color: #ffffff !important;
}

.create-account-btn a:hover {
  color: #ffffff !important;
  border-color: #ffffff;
  /* text-decoration: none; */
}
