/* .logo {
  display: flex;
  align-items: flex-start;
  position: relative;
  right: 120px;
  bottom: 20px;
  gap: 5px;
  width: 86px;
  height: 62px;
}

.logo-text {
  display: flex;
  align-items: flex-start;
  position: relative;
  font-family: Roboto;
  font-size: 24px;
  font-weight: 700;
  bottom: 65px;
  right: 20px;
} */

.brand-text-orange {
  color: #ff6b00;
}

.brand-text-blue {
  color: #02aaeb;
}

.brand-text-green {
  color: #45c717;
}

.login-svg-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: -1;
  overflow: hidden;
}

.login-svg-container svg {
  width: 100vw;
  height: 100vh;
  object-fit: cover;
  position: relative;
}

.Bread-crumb3 {
  margin: auto;
  display: flex;
  align-items: center;
  position: relative;
  margin-bottom: 40px; /* Adjust as needed */
}

.Congrats-Banner {
  margin: auto;
  display: flex;
  align-items: center;
  position: relative;
  margin-bottom: 60px; /* Adjust as needed */
}

.Confirmation-Text {
  font-family: Inter;
  font-size: 24px;
  font-weight: 500;
  text-align: center;
  color: black;
  margin-bottom: 10px; /* Adjust as needed */
}

.NextButton {
  margin-top: 20px; /* Adjust as needed */
}
