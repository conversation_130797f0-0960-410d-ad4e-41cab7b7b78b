.background-container {
  background-color: #e7eeff;
  position: relative;
  width: 100vw;
  height: 100vh;
  margin-left: -55%;
}

.background {
  background-color: #e7eeff;
  background-size: cover;
}

.courses-container {
  display: grid;
  grid-template-columns: repeat();
  grid-template-rows: repeat();
  position: relative;
  padding: 40px;
}

.introduction-container {
  display: flex;
  flex-direction: column;
  text-align: center;
  width: 100%;
  height: 100%;
  align-items: center; /* Add this to center the container horizontally */
  justify-content: center; /* Add this to center the container vertically */
}

.title {
  display: flex;
  font-family: Inter;
  font-weight: 700;
  font-size: 58px;
  max-width: 842px;
  max-height: 140px;
  color: #263238;
  align-items: left;
  justify-content: left;
  margin-left: 24px;
  padding: 5px;
}

/* .points {
  background-color: #197eb1;
} */

.description {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: left;
  font-family: Inter;
  font-weight: 500;
  font-size: 32px;
  max-width: 1125px;
  max-height: 121px;
  color: #000000;
  margin-top: 60px;
  margin-bottom: 60px;
}
