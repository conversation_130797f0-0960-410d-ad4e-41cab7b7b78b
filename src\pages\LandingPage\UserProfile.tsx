// UserProfile.tsx
import { ReactComponent as Userprof } from '../../assets/user-prof.svg';
import './DropDown.css';
import { useUser } from '../../context/UserContext';

const UserProfile: React.FC = () => {
  const { userName } = useUser();
  return (
    <>
      <div
        style={{
          backgroundColor: '#E8E8E8',
          color: '#000000',
          padding: '8px 30px',
          fontSize: '25px',
          fontWeight: 'bold',
          height: '50px',
          marginLeft: '8px',
          display: 'flex', // Align children horizontally
          alignItems: 'center',
          gap: '8px', // Add spacing between children
          cursor: 'pointer',
          borderRadius: '48px',
          position: 'relative',
          zIndex: 1, // Set as relative for dropdown positioning
          minWidth: 'fit-content', // Ensure the button fits its content
        }}
      >
        <Userprof
          style={{
            height: '40px',
            width: '40px',
            pointerEvents: 'none',
            zIndex: 0,
          }}
        />
        <span>{userName}</span>
      </div>
    </>
  );
};

export default UserProfile;
