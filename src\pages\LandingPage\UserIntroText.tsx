// import React from 'react';
import React, { useState, useEffect } from 'react';
import { NavLink } from 'react-router-dom';

const UserIntroText: React.FC = () => {
  const [paddingLeft, setPaddingLeft] = useState('20px');
  const [fontSize, setFontSize] = useState('80px');
  const [paragraphWidth, setParagraphWidth] = useState('581px');
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setPaddingLeft('10px');
        setFontSize('50px');
        setParagraphWidth('400px');
      } else {
        setPaddingLeft('20px');
        setFontSize('70px');
        setParagraphWidth('410px');
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Call on mount to set initial value

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <div
      style={{
        width: 'auto',
        // height: '478px',
        height: 'auto',
        position: 'relative',
        top: '85px',
        left: '0px',
        padding: '60px 0',
        // gap: '10px',
        fontFamily: 'Roboto',
        // fontSize: '90px',
        fontSize: fontSize,
        fontWeight: '600',
        color: '#16325C',
        textAlign: 'left',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
        whiteSpace: 'normal',

        // backgroundColor: "pink",
      }}
    >
      <h1
        style={{
          width: '610px',
          // width: paragraphWidth,
          height: '180px',
          border: '4px',
          padding: '8px 8px 8px 20px',
          paddingLeft: paddingLeft,
          gap: '8px',
          // fontSize: '80px',
          fontSize: fontSize,
          lineHeight: '77px',
          // marginBottom: '20px',
          // backgroundColor: "pink",
        }}
      >
        Online Learning Made Fun
      </h1>
      <p
        style={{
          // width: '581px',
          width: paragraphWidth,
          height: '50px',
          fontSize: '30px',
          fontWeight: '500',
          lineHeight: '40px',
          color: '#16325C',
          textAlign: 'left',
          whiteSpace: 'normal',
          paddingLeft: '20px',
        }}
      >
        Learn anytime, anywhere for grades K-12
      </p>
      <NavLink to='/signup'>
        <button
          style={{
            // width: '180px',
            // height: '58px',
            fontFamily: 'Roboto',
            borderRadius: '26px',
            padding: '8px 22px',
            // padding: '1px 10px',
            // backgroundColor: '#FF4D00',
            // marginTop: '10px',
            color: 'white',
            fontSize: '22px',
            marginLeft: '20px',
            alignSelf: 'flex-start',
            marginTop: '60px',
            cursor: 'pointer',
            backgroundColor: isHovered ? '#015EAF' : '#FF4D00',
          }}
          onMouseEnter={() => setIsHovered(true)} // Set hover state to true
          onMouseLeave={() => setIsHovered(false)}
        >
          <span
            style={
              {
                // width: '138px',
                // height: '32px',
                // fontWeight: '700',
                // fontFamily: 'Roboto',
                // lineHeight: '32px',
                // alignSelf: 'center',
              }
            }
          >
            Join Now
          </span>
        </button>
      </NavLink>
    </div>
  );
};

export default UserIntroText;
