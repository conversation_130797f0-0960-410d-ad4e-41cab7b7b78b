import { useCallback } from 'react';
import { supabase } from '../../services/supabase/supabaseClient';
import { useUser } from '../../context/UserContext';

interface UsePointsOptions {
  userId?: string | null;
}

const usePoints = ({ userId }: UsePointsOptions) => {
  const { setPointsTotal } = useUser();

  const givePointsForVideo = useCallback(
    async (videoLink: string) => {
      console.log(
        'givePointsForVideo called with userId:',
        userId,
        'and videoLink:',
        videoLink
      );

      if (!userId) {
        console.error('No userId provided');
        return { success: false, error: 'No userId provided' };
      }

      try {
        // First, get the user_id from the username
        console.log('Fetching user_id for username:', userId);
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('user_id')
          .eq('username', userId)
          .single();

        if (userError) {
          console.error(`Error fetching user ID: ${userError.message}`);
          return {
            success: false,
            error: `Error fetching user ID: ${userError.message}`,
          };
        }

        const userIdFromDB = userData?.user_id;
        console.log('Retrieved userIdFromDB:', userIdFromDB);

        if (!userIdFromDB) {
          console.error('User ID not found');
          return { success: false, error: 'User ID not found' };
        }

        const { data: watchedData, error: watchedError } = await supabase
          .from('user_watched_videos')
          .select('*')
          .eq('user_id', userIdFromDB)
          .eq('video_link', videoLink)
          .single();

        if (watchedError && watchedError.code !== 'PGRST116') {
          console.error(
            `Error checking if video was watched: ${watchedError.message}`
          );
          return {
            success: false,
            error: `Error checking if video was watched: ${watchedError.message}`,
          };
        }

        if (watchedData) {
          console.error('Video already watched');
          return {
            success: false,
            alreadyWatched: true,
            error: 'Video already watched',
          };
        }

        // Record that the user has watched this video
        const { error: insertError } = await supabase
          .from('user_watched_videos')
          .insert([
            {
              user_id: userIdFromDB,
              video_link: videoLink,
            },
          ]);

        if (insertError) {
          console.error(
            `Error recording watched video: ${insertError.message}`
          );
          return {
            success: false,
            error: `Error recording watched video: ${insertError.message}`,
          };
        }

        console.log('Successfully recorded video as watched');

        // Fetch current points data
        console.log('Fetching points data for user_id:', userIdFromDB);
        const { data: pointsData, error: fetchError } = await supabase
          .from('user_points_history')
          .select('points_total, points_earned')
          .eq('user_id', userIdFromDB)
          .single();

        if (fetchError) {
          console.error(`Error fetching points data: ${fetchError.message}`);
          return {
            success: false,
            error: `Error fetching points data: ${fetchError.message}`,
          };
        }

        console.log('Current points data:', pointsData);

        // Define points to award
        const pointsForWatchingVideo = 10;

        // Calculate new points
        const pointsTotal =
          (pointsData?.points_total || 0) + pointsForWatchingVideo;
        const newPointsEarned =
          (pointsData?.points_earned || 0) + pointsForWatchingVideo;

        console.log('Updating points to:', { pointsTotal, newPointsEarned });

        // Update points in database
        const { data: updatePoints, error: updateError } = await supabase
          .from('user_points_history')
          .update({
            points_total: pointsTotal,
            points_earned: newPointsEarned,
          })
          .eq('user_id', userIdFromDB)
          .select('*');

        if (updateError) {
          console.error(`Error updating points: ${updateError.message}`);
          return {
            success: false,
            error: `Error updating user points: ${updateError.message}`,
          };
        }

        if (!updatePoints || updatePoints.length === 0) {
          console.error('No points were updated');
          return {
            success: false,
            error: 'Total points was not updated for this user',
          };
        }

        console.log('Points updated successfully:', updatePoints);

        // Update the points in the UserContext
        setPointsTotal(updatePoints[0].points_total);
        console.log(
          'Updated context with new points total:',
          updatePoints[0].points_total
        );

        // Return success response
        return {
          success: true,
          message: `Points updated for ${userId}`,
          pointsAwarded: pointsForWatchingVideo,
          totalPoints: updatePoints[0].points_total,
          pointsEarned: updatePoints[0].points_earned,
        };
      } catch (e) {
        const errorMessage = e instanceof Error ? e.message : 'Unknown error';
        console.error(`Error in updating points: ${errorMessage}`);
        return { success: false, error: errorMessage };
      }
    },
    [userId, setPointsTotal]
  );

  return { givePointsForVideo };
};

export default usePoints;
