/* .background-container {
  background-color: #75a3e7;
  position: relative;
  width: 100vw;
  height: 200vh;
  margin-left: -55%;
} */

/* .background {
  background-color: #d6e1fb;
  background-size: cover;
  width: 100%;
  height: 200vh; 
  position: relative; 
  overflow: hidden; 
} */

.courses-container {
  display: grid;
  grid-template-columns: repeat();
  grid-template-rows: repeat();
  position: relative;
  padding: 40px;
  margin-bottom: 100px;
}

.Internet-Use {
  grid-column: 2/ 3;
  grid-row: 3;
}

.introduction-container {
  display: flex;
  flex-direction: column;
  text-align: flex-start;
  width: 100%;
  height: 200%;
  align-items: center;
  justify-content: center;
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-80px);
  }
  60% {
    transform: translateY(-30px);
  }
}

.teaching-owls {
  margin-top: 200px;
  width: 40%;
  max-width: 550px;
  height: auto;
  transition:
    transform 0.3s ease,
    opacity 0.3s ease;
}

.teaching-owls:hover {
  animation: bounce 0.6s;
}

.title {
  display: flex;
  font-family: Inter;
  font-weight: 700;
  font-size: 58px;
  max-width: 842px;
  max-height: 140px;
  color: #263238;
  align-items: left;
  justify-content: left;
  margin-left: 24px;
  padding: 5px;
  margin-bottom: auto;
}

.points {
  background-color: #7c7c7c;
  color: white;
  padding-left: 16px;
}

.description {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: left;
  font-family: Inter;
  font-weight: 500;
  font-size: 28px;
  /* max-width: 1125px; */
  /* max-height: 121px; */
  color: #000000;
  margin-top: 60px;
  margin-bottom: 60px;
  /* background-color: blue; */
}
