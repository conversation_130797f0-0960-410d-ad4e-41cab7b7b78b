import { useNavigate } from 'react-router-dom';
import UserForm from './UserForm';
import { supabase } from '../../services/supabase/supabaseClient';
import { ReactComponent as BridgingTechLogo } from '../../assets/BridgingTechLogoTwo.svg';
import { ReactComponent as Breadcrumb2 } from '../../assets/Breadcrumb2.svg';
import { ReactComponent as InitialSignupPageBG } from '../../assets/InitialSignupPageBG.svg';
import './SignupPageAbove14.css';

const SignupPageAbove14: React.FC = () => {
  const navigate = useNavigate();

  const onFormSubmit = async (
    email: string,
    username: string,
    password: string,
    termsChecked: boolean,
    parent_consent_given: boolean = true,
    parent_consent_date: Date = new Date()
  ) => {
    if (!password) {
      alert('Password Required');
      return;
    }

    if (!email) {
      alert('Email Required');
      return;
    }

    if (!username) {
      alert('User Name Required');
      return;
    }

    if (!termsChecked) {
      alert('You must agree to the Terms of Services and Privacy Policy.');
      return;
    }

    try {
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username: username,
          },
        },
      });

      if (authError) throw authError;

      const { data, error } = await supabase
        .from('users')
        .insert([
          {
            username,
            parent_consent_given,
            parent_consent_date,
            auth_id: authData.user?.id,
            user_email: email,
          },
        ])
        .select()
        .single();

      if (error) throw error;

      const { error: pointsError } = await supabase
        .from('user_points_history')
        .insert([
          {
            user_id: data.user_id,
            points_total: 0,
            points_earned: 0,
            login_streak: 0,
          },
        ]);
      if (pointsError) throw pointsError;

      navigate('/signup/confirmation2');
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div>
      <BridgingTechLogo className='logo' />
      <div className='logo-text'>
        <span className='brand-text-orange'>Bridging</span>
        <span className='brand-text-blue'>Tech</span>
        <span className='brand-text-green'>Ed</span>
      </div>
      <div className='login-svg-container'>
        <InitialSignupPageBG
          preserveAspectRatio='none'
          viewBox='0 0 1920 1080'
        />
      </div>
      <Breadcrumb2 className='Bread-crumb2' />
      <div className='form-section'>
        <UserForm onFormSubmit={onFormSubmit} />
      </div>
    </div>
  );
};

export default SignupPageAbove14;
