import React, { FormEvent, useState } from 'react';
import './Signup.css';
import Header from '../LandingPage/Header';
import axios from 'axios';

const Login: React.FC = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    user_email: '',
  });

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    const config = {
      method: 'post',
      url: `${import.meta.env.VITE_APP_BASE_URL}/api/auth/login`,
      data: { ...formData },
    };

    try {
      const response = await axios(config);
      alert('You have successfully logged-in!');
      return response;
    } catch (error) {
      alert('Unable to login!');
      console.log(error);
    }
  };

  return (
    <div className='split-screen-container'>
      <Header />
      <div className='sign-up-container'>
        <div className='header'>
          <div className='text'>Login</div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className='inputs'>
            <div className='input-container'>
              <label>User Name</label>
              <div className='input'>
                <input
                  type='text'
                  name='username'
                  value={formData.username}
                  onChange={handleChange}
                />
              </div>
            </div>

            <div className='input-container'>
              <label>User Email</label>
              <div className='input'>
                <input
                  type='email'
                  name='user_email'
                  value={formData.user_email}
                  onChange={handleChange}
                />
              </div>
            </div>

            <div className='input-container'>
              <label>Password</label>
              <div className='input'>
                <input
                  type='password'
                  name='password'
                  value={formData.password}
                  onChange={handleChange}
                />
              </div>
            </div>
          </div>

          <div className='submit-container'>
            <button type='submit' className='submit'>
              Login
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Login;
