module.exports = {
  process(src, filename) {
    // Check if the filename contains '?react' and adjust the mock accordingly
    const isReactImport = filename.endsWith('?react');
    let code;

    if (isReactImport) {
      // Simulate the module structure provided by vite-plugin-svgr for React components
      code = `module.exports = {
          ReactComponent: () => 'Mock SVG React Component for ${filename}',
          default: '${filename}',
        };`;
    } else {
      // Fallback for non-React imports or other scenarios
      code = `module.exports = '${filename}';`;
    }

    return { code };
  },
};
