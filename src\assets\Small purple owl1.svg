<svg xmlns="http://www.w3.org/2000/svg" width="248" height="347" fill="none" xmlns:v="https://vecta.io/nano"><g clip-path="url(#D)"><g fill="#7894c6"><path d="M92.645 302.325c4.804-8.318 8.291-17.969 7.266-27.653l-31.606-4.545c-6.992 1.835-13.795 5.411-18.327 11.323s-6.404 14.411-3.512 21.379c-3.844.851-7.614 2.453-10.577 5.179s-5.044 6.667-5.106 10.814c-.036 2.308.718 4.851 2.626 5.978 2.195 1.296 4.909.255 7.264-.678 12.344-4.89 25.692-6.286 38.223-10.612 3.205-1.107 6.422-2.447 8.971-4.777 1.955-1.786 3.433-4.077 4.779-6.408zm71.385 20.558c-9.253-2.581-18.497-7.036-24.54-14.672l19.399-25.362c6.279-3.581 13.641-5.787 21.016-4.734s14.659 5.814 17.468 12.816c3.342-2.082 7.156-3.575 11.18-3.701s8.268 1.234 11.215 4.152c1.64 1.624 2.881 3.969 2.307 6.108-.662 2.463-3.329 3.618-5.664 4.599-12.24 5.143-22.752 13.486-34.732 19.164-3.065 1.452-6.3 2.745-9.752 2.864-2.646.093-5.305-.511-7.897-1.234z"/><path d="M33.055 253.222c16.03 42.838 29.432 41.742 45.781 49.898s65.677 12.472 83.522 5.053c24.946-10.372 27.952-23.623 37.156-54.951 7.776-26.468 13.463-43.584 9.413-71.373-5.416-37.158-20.476-58.49-46.569-76.425l-2.477-1.661L150.467 65c-4.624 8.843-13.871 26.727-13.871 27.525-18.499-5.63-36.527-2.972-55.486 7.91L75.66 76.369c-5.119 9.685-15.853 31.239-17.835 39.976-13.509 10.475-30.017 50.346-32.494 69.294s-4.773 34.19 7.724 67.583z"/></g><path d="M64.703 241.087c1.565 15.339-6.313 41.803 10.331 57.287 12.449 11.581 24.915 10.139 41.792 12.209 17.124 2.1 29.988 7.993 44.139-1.879 16.465-11.486 19.565-37.252 14.556-49.304s-3.139-31.132-17.374-41.322c-8.016-5.738-14.304-7.347-23.948-9.391-17.759-3.765-31.116-8.252-46.487 1.409-11.627 7.307-24.574 15.652-23.009 30.991z" fill="#d1d0ff"/><path d="M138.792 159.552c-.188-3.194 1.461-6.164 4.494-6.737s6.492 4.664 6.492 4.664l-4.494 8.292s-6.281-2.645-6.492-6.219z" fill="#fff"/><path d="M144.055 160.112c1.629-1.533 1.267-4.574-.821-6.795l-.332-.326c.929-.525 1.949-.821 3.023-.821 4.339 0 7.858 4.786 7.858 10.682s-3.519 10.682-7.858 10.682-7.857-4.786-7.857-10.682c0-1.056.115-2.082.326-3.048 1.955 1.407 4.291 1.594 5.661.308z" fill="url(#B)"/><path d="M78.792 159.552c-.189-3.194 1.46-6.164 4.494-6.737s6.491 4.664 6.491 4.664l-4.494 8.292s-6.281-2.645-6.491-6.219z" fill="#fff"/><path d="M84.289 160.112c1.629-1.533 1.267-4.574-.821-6.795l-.332-.326c.929-.525 1.949-.821 3.023-.821 4.339 0 7.857 4.786 7.857 10.682s-3.518 10.682-7.857 10.682-7.857-4.786-7.857-10.682c0-1.056.115-2.082.326-3.048 1.949 1.407 4.291 1.594 5.661.308z" fill="url(#C)"/><path d="M20.19 119.699L1.482 107.584c-.092-.07-.167-.161-.218-.265l-1.096-1.641 1.874.237a.85.85 0 0 1 .338.117l19.146 11.601a.84.84 0 0 1 .382.532c.051.223.013.461-.107.666l-.281.474a.97.97 0 0 1-.243.307c-.102.085-.219.149-.345.186a.93.93 0 0 1-.385.031c-.127-.018-.248-.062-.354-.13z" fill="#263238"/><g fill="#7894c6"><path d="M64.869 147.578c-10.831 1.72-21.921-4.013-29.038-12.805s-10.701-20.238-12.309-31.68c-5.465 8.829-6.431 20.574-2.491 30.267-2.21-3.095-3.99-6.531-5.266-10.166-2.148 6.025-1.047 12.825 1.042 18.873 6.561 18.992 24.125 28.769 45.949 18.814m138.693 29.315c2.355 9.406 10.247 16.539 19.051 19.41s18.371 1.977 27.274-.529c-4.634 7.402-12.901 12.108-21.304 12.128a30.25 30.25 0 0 0 9.192.904c-3.69 3.762-9.031 5.132-14.15 5.445-16.078.985-29.087-10.078-29.087-31.198"/><path d="M110.897 128.851c-3.767-8.88-1.873-19.666 3.266-27.742s13.112-13.73 21.529-17.69c-8.009-2.682-17.24-.855-23.788 4.706 1.875-2.59 4.11-4.89 6.611-6.804-5.109-.477-10.078 2.002-14.247 5.156-13.092 9.905-16.626 27.144-4.057 43.571"/></g><g filter="url(#A)"><path d="M116.063 196.621c.363.757 1.441.757 1.804 0l12.239-25.563a1 1 0 0 0-.902-1.432h-24.478a1 1 0 0 0-.902 1.432l12.239 25.563z" fill="#ff9a00"/></g></g><defs><filter id="A" x="99.725" y="169.626" width="34.48" height="35.563" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="A"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="B"/><feOffset dy="4"/><feGaussianBlur stdDeviation="2"/><feComposite in2="B" operator="out"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/><feBlend in2="A"/><feBlend in="SourceGraphic"/></filter><linearGradient id="B" x1="145.93" y1="152.168" x2="145.93" y2="173.535" gradientUnits="userSpaceOnUse"><stop stop-color="#424242"/><stop offset="1" stop-color="#212121"/></linearGradient><linearGradient id="C" x1="86.16" y1="152.168" x2="86.16" y2="173.535" gradientUnits="userSpaceOnUse"><stop stop-color="#424242"/><stop offset="1" stop-color="#212121"/></linearGradient><clipPath id="D"><path fill="#fff" d="M0 0h247.774v346.653H0z"/></clipPath></defs></svg>