import React, { FormEvent, useState } from 'react';

import { useNavigate } from 'react-router-dom';
import { supabase } from '../../services/supabase/supabaseClient';
import { useUser } from '../../context/UserContext';

import './LoginPage.css';
// import young_lady from '../assets/aiUpscaledYoungLady.png';
// import LoginButton from './LoginButton';
import axios from 'axios';
import CreateAccountButton from './CreateAccountButton';
import { ReactComponent as GreenOwl } from '../../assets/greenOwl2o.svg';
import { ReactComponent as BridgingTechLogo } from '../../assets/BridingTechLogo.svg';
import { Link } from 'react-router-dom';
import LoginFormButton from './LoginFormButton';

const LoginPage: React.FC = () => {
  const Navigate = useNavigate();

  const { setUserName, setPointsTotal, setLoginStreak } = useUser();

  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });

  const handleForgotPassword = () => {
    const email = prompt('Enter your email:');
    if (email) {
      supabase.auth.resetPasswordForEmail(email);
    }
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    try {
      const { data: userData, error: loginError } =
        await supabase.auth.signInWithPassword({
          email: formData.email,
          password: formData.password,
        });

      if (loginError) {
        throw new Error('Login failed: ' + loginError.message);
      }

      const loginConfig = {
        method: 'post',
        url: `${import.meta.env.VITE_APP_BASE_URL}/api/auth/login`,
        headers: {
          Authorization: `Bearer ${userData.session.access_token}`,
        },
        data: { userName: userData.user.user_metadata.username },
      };

      // set cursor to loading symbol
      document.body.classList.add('loading-cursor');

      const response = await axios(loginConfig);

      const { pointsTotal, streak } = response.data;

      const {
        data: { user },
      } = await supabase.auth.getUser();
      const name = user?.user_metadata.username || 'Guest';

      setPointsTotal(pointsTotal);
      setLoginStreak(streak);
      setUserName(name);

      document.body.classList.remove('loading-cursor');

      Navigate('/');
      return response;
    } catch (error) {
      alert('Unable to login!');
      console.log(error);
    }
  };

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        position: 'fixed',
        top: 0,
        left: 0,
        zIndex: '-1',
        // backgroundImage: `url(${CloudsImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundColor: '#CDEBFB',
      }}
    >
      <div className='split-screen-container'>
        <div className='left-container'>
          <div className='image-container'>
            <GreenOwl />

            {/* <img src={young_lady} alt='Young Lady with Laptop' /> */}
          </div>
          <div className='text-container'>
            <h1 className='main-title'>
              Join Bridging Tech Ed to start learning today!
            </h1>

            <h2 className='tagline'>
              Log in to Bridging Tech Ed to get started!
            </h2>
          </div>
          <div className='privacy-policy-container'>
            <h3 className='privacy-policy-text'>
              By logging in to Bridging Tech Ed, you agree to our Terms of use
              and{' '}
              <a
                href='https://www.bridgingtech.org/'
                className='privacy-policy-link'
              >
                Privacy Policy.
              </a>
            </h3>
          </div>
        </div>{' '}
        {/*left container end*/}
        <div className='sign-in-container'>
          <div className='header'>
            <div className='logo-container'>
              <Link to='/'>
                <BridgingTechLogo />
              </Link>
            </div>

            <div className='text'>Sign in to Bridging Tech Ed</div>
          </div>

          <form onSubmit={handleSubmit}>
            <div className='inputs'>
              {/* <div className='input-container'></div> */}

              <div className='input-container'>
                <label>Email</label>
                <div className='input'>
                  <input
                    type='text'
                    name='email'
                    value={formData.email}
                    onChange={handleChange}
                  />
                </div>
              </div>

              <div className='input-container'>
                <label>Password</label>
                <div className='input'>
                  <input
                    type='password'
                    name='password'
                    value={formData.password}
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>

            <div className='forget-password-text'>
              Forgot username or password?
              <a
                href='#'
                onClick={handleForgotPassword}
                className='click-here-btn'
              >
                Click here
              </a>
            </div>

            <div className='submit-container'>
              {/* <LoginButton /> */}
              <LoginFormButton />
            </div>
          </form>

          <div className='divider-break'>
            <span className='line-behind-text'></span>
            <p className='new-to-btech-text'>New to Bridging Tech Ed?</p>
            <span className='line-behind-text'></span>
          </div>

          <div className='create-account-btn'>
            <Link to='/Signup'>
              <CreateAccountButton />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
