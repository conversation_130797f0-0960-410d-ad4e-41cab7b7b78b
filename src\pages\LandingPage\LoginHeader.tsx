/* eslint-disable react-refresh/only-export-components */
/// <reference types="vite-plugin-svgr/client" />

import UserProfile from './UserProfile';
import ArrowDropdown from './ArrowDropdown';
import { ReactComponent as Flame } from '../../assets/flames.svg';
import { ReactComponent as Coin } from '../../assets/coin.svg';
import { ReactComponent as BridgingTechLogo } from '../../assets/BridingTechLogo.svg';
import { NavLink } from 'react-router-dom';
import { useUser } from '../../context/UserContext';
import NavButton from './NavButton';

const Header: React.FC<{ navLinkText?: string }> = ({ navLinkText = '' }) => {
  // const navigate = useNavigate();

  const { pointsTotal, loginStreak } = useUser();

  let navPath;
  if (navLinkText === 'Chatbot') {
    navPath = '/chatbot';
  } else {
    navPath = '/';
  }

  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       const result = await pool.query('SELECT user_id FROM users WHERE username = $1', [userName]);
  //       console.log(result);
  //     } catch (error){
  //       console.error("Error fetching user data:", error);
  //     }
  //   }

  //   if (userName !== "Guest"){
  //     fetchData();
  //   }
  // },[userName]);

  return (
    <header
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        backgroundColor: 'white',
        padding: '8px 16px 8px 16px',
        zIndex: 2,
      }}
    >
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '10px 24px',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <BridgingTechLogo
            style={{ height: '40px', width: 'auto', marginRight: '10px' }}
          />
          <span
            style={{
              fontSize: '18px',
              fontWeight: 'bold',
              color: '#FF6B00',
              marginRight: '5px',
            }}
          >
            Bridging
          </span>
          <span
            style={{
              fontSize: '18px',
              fontWeight: 'bold',
              color: '#02AAEB',
              marginRight: '5px',
            }}
          >
            Tech
          </span>
          <span
            style={{ fontSize: '18px', fontWeight: 'bold', color: '#45C717' }}
          >
            Ed
          </span>
          <NavLink to={navPath}>
            <NavButton text={navLinkText} />
          </NavLink>
        </div>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <UserProfile />
          <ArrowDropdown />
          <Flame
            style={{
              height: '34px',
              width: '34px',
              marginBottom: '5px',
            }}
          />
          <span
            style={{
              color: '#000000',
              fontSize: '30px',
              fontWeight: 'bold',
              marginLeft: '24px',
              marginRight: '40px',
            }}
          >
            {loginStreak}
          </span>
          <Coin style={{ height: '38px', width: '38px' }} />
          <span
            style={{
              color: '#000000',
              fontSize: '30px',
              fontWeight: 'bold',
              marginRight: '30px',
              marginLeft: '24px',
            }}
          >
            {pointsTotal}
          </span>
          {/* <img src={young_lady} alt='Young Lady with Laptop' /> */}
        </div>
      </div>
    </header>
  );
};

export default Header;
